import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { PreviewAndSubmitFormComponent } from './preview-and-submit-form.component';
import { CompanyStateService } from '../../../core/services/company-state.service';

describe('PreviewAndSubmitFormComponent', () => {
  let component: PreviewAndSubmitFormComponent;
  let fixture: ComponentFixture<PreviewAndSubmitFormComponent>;
  let mockCompanyStateService: jasmine.SpyObj<CompanyStateService>;

  beforeEach(async () => {
    mockCompanyStateService = jasmine.createSpyObj('CompanyStateService', [
      'getCurrentOnboardingState'
    ]);

    mockCompanyStateService.getCurrentOnboardingState.and.returnValue({
      companyDetails: null,
      billingDetails: null,
      userDetails: null,
      currentStep: 0
    });

    await TestBed.configureTestingModule({
      imports: [
        PreviewAndSubmitFormComponent,
        ReactiveFormsModule,
        NoopAnimationsModule
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: CompanyStateService, useValue: mockCompanyStateService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(PreviewAndSubmitFormComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with onboarding state data', () => {
    expect(mockCompanyStateService.getCurrentOnboardingState).toBeDefined();
  });

  it('should emit create company event', () => {
    spyOn(component.createCompany, 'emit');
    component.onCreateCompany();
    expect(component.createCompany.emit).toHaveBeenCalled();
  });

  it('should emit edit section event', () => {
    spyOn(component.editSection, 'emit');
    const mockStep = 'CompanyDetails' as any;
    component.onEditSection(mockStep);
    expect(component.editSection.emit).toHaveBeenCalledWith(mockStep);
  });

  it('should update create button state', () => {
    component.updateCreateButtonState();
    expect(component.updateCreateButtonState).toBeDefined();
  });

  it('should handle component destruction', () => {
    component.ngOnDestroy();
    expect(component.ngOnDestroy).toBeDefined();
  });
});
