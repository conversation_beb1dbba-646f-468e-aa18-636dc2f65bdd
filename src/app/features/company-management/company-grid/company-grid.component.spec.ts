import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { CompanyGridComponent } from './company-grid.component';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { NzModalService } from 'ng-zorro-antd/modal';

describe('CompanyGridComponent', () => {
  let component: CompanyGridComponent;
  let fixture: ComponentFixture<CompanyGridComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockRegisterService: jasmine.SpyObj<RegisterService>;
  let mockModalService: jasmine.SpyObj<NzModalService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', ['error', 'success', 'warning']);
    mockRegisterService = jasmine.createSpyObj('RegisterService', ['getAllCompanies', 'activeInactiveCompany']);
    mockModalService = jasmine.createSpyObj('NzModalService', ['confirm']);
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockActivatedRoute = {
      snapshot: { params: {} },
      params: of({}),
      queryParams: of({})
    };

    await TestBed.configureTestingModule({
      imports: [
        CompanyGridComponent,
        NoopAnimationsModule
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: RegisterService, useValue: mockRegisterService },
        { provide: NzModalService, useValue: mockModalService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CompanyGridComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.isLoading).toBeFalse();
    expect(component.companyTableData).toEqual([]);
    expect(component.totalCompanies).toBe(0);
    expect(component.newCompanies).toBe(0);
    expect(component.discontinuedCompanies).toBe(0);
  });

  it('should fetch companies on init', () => {
    mockRegisterService.getAllCompanies.and.returnValue(of({
      success: true,
      data: { content: [], totalElements: 0 }
    }));

    component.ngOnInit();

    expect(component.ngOnInit).toBeDefined();
  });

  it('should handle table data click', () => {
    const mockTableData = {
      action: 'view',
      rowData: { id: 1001, name: 'TechCorp Solutions' }
    } as any;

    spyOn(component, 'onTableDataClick');
    component.onTableDataClick(mockTableData);

    expect(component.onTableDataClick).toHaveBeenCalledWith(mockTableData);
  });

  it('should navigate to view company', () => {
    const mockCompany = { id: 1001, name: 'TechCorp Solutions' } as any;

    component.viewCompany(mockCompany);

    expect(mockRouter.navigate).toHaveBeenCalledWith([1001], { relativeTo: mockActivatedRoute });
  });

  it('should handle edit company action', () => {
    const mockTableData = {
      action: 'edit',
      rowData: { id: 1002, name: 'InnovateTech Industries' }
    } as any;

    component.onTableDataClick(mockTableData);

    expect(component.onTableDataClick).toBeDefined();
  });

  it('should show confirmation modal for status toggle', () => {
    const mockCompany = { id: 1003, name: 'GlobalTech Enterprises', isActive: true } as any;

    component.toggleCompanyStatus(mockCompany);

    expect(component.toggleCompanyStatus).toBeDefined();
  });

  it('should apply filters correctly', () => {
    component.allCompanies = [
      { id: 1004, name: 'Digital Dynamics Corp', isActive: true },
      { id: 1005, name: 'Future Systems Ltd', isActive: false }
    ] as any[];

    component.applyFilters();

    expect(component.companyTableData.length).toBeGreaterThanOrEqual(0);
  });

  it('should handle show onboarding', () => {
    component.showOnboarding = false;
    component.showOnboarding = true;

    expect(component.showOnboarding).toBeTrue();
  });

  it('should close onboarding form', () => {
    component.closeOnboardingForm();

    expect(component.showOnboarding).toBeFalse();
  });
});
